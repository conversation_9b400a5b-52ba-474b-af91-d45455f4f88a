import { Entity } from "@minecraft/server";
import { timeout } from "../../../general_attacks/timingUtils";
import { executeSplashAttack } from "./attacks/splash";

/**
 * When the attack should apply in the animation
 */
const ATTACK_TIMING = 35; // Apply damage at tick 35

/**
 * The total animation time in ticks
 */
const ANIMATION_TIME = 88; // Total animation time in ticks

/**
 * Cooldown before executing the next attack
 */
const COOLDOWN_TIME = 20;

/**
 * Handles all mechanics for the Zombie Brute
 * This includes attack selection, attack execution, and cooldown management
 *
 * @param zombieBrute The zombie brute entity
 */
export async function zombieBruteMechanics(zombieBrute: Entity): Promise<void> {
  // Skip if entity is not valid
  try {
    if (!zombieBrute) return;

    // Skip if entity is spawning or dead
    const isSpawning = zombieBrute.getProperty("ptd_dbb:spawning") as boolean;
    const isDead = zombieBrute.getProperty("ptd_dbb:dead") as boolean;
    if (isSpawning || isDead) return;

    timeout((finish) => {
      if (finish) {
        executeSplashAttack(zombieBrute);
      }
    }, ATTACK_TIMING);

    await timeout((finish) => {
      if (finish) {
        zombieBrute.triggerEvent("ptd_dbb:reset_attack");
      }
    }, ANIMATION_TIME);

    timeout((finish) => {
      if (finish) {
        zombieBrute.triggerEvent("ptd_dbb:attack");
      }
    }, ANIMATION_TIME + COOLDOWN_TIME);
  } catch (e) {
    return;
  }
}
